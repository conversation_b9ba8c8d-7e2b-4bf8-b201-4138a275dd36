{"rustc": 1842507548689473721, "features": "[\"alloc\", \"derive\"]", "declared_features": "[\"alloc\", \"default\", \"derive\"]", "target": 723370850876025358, "profile": 15657897354478470176, "path": 12015973833724570486, "deps": [[4022439902832367970, "zerofrom_derive", false, 12393517968937185791]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerofrom-df81efceec864655\\dep-lib-zerofrom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
[workspace]
members = [
    ".",
    "server",
    "migration",
    "tools/test-reporter",
    "crates/app_common",
    "crates/app_domain",
    "crates/app_application",
    "crates/app_infrastructure",
    "crates/app_interfaces"
]

[workspace.dependencies]
# 内部crate依赖
app_common = { path = "crates/app_common" }
app_domain = { path = "crates/app_domain" }
app_application = { path = "crates/app_application" }
app_infrastructure = { path = "crates/app_infrastructure" }
app_interfaces = { path = "crates/app_interfaces" }

# 核心依赖版本统一管理 - 2025年最新稳定版本
axum = { version = "0.8", features = ["ws", "macros"] }
tokio = { version = "1.47", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
uuid = { version = "1.11", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
log = "0.4"
async-trait = "0.1"
anyhow = "1.0"
thiserror = "2.0"

# 数据库 ORM - 最新稳定版本
sea-orm = { version = "1.1", features = ["sqlx-postgres", "sqlx-sqlite", "runtime-tokio-native-tls", "mock"] }
sea-orm-migration = { version = "1.1", features = ["runtime-tokio-native-tls", "sqlx-postgres"] }

# DragonflyDB缓存支持 - 最新稳定版本
fred = { version = "10.1" }
redis = { version = "0.27", features = ["tokio-comp", "connection-manager"] }

# 熔断器和限流器支持 - 最新稳定版本
failsafe = "1.3"  # 熔断器模式和错误恢复
tower_governor = "0.4"  # Tower/Axum限流中间件

# SQLx - 最新稳定版本
sqlx = { version = "0.8", features = ["runtime-tokio-native-tls", "postgres", "chrono", "uuid"] }

# 数据验证 - 最新稳定版本
validator = { version = "0.20", features = ["derive"] }

# 正则表达式 - 最新稳定版本
regex = "1.11"

# 性能监控和指标收集 - 最新稳定版本
metrics = "0.24"
metrics-exporter-prometheus = "0.17"
metrics-util = "0.20"

# 系统信息收集 - 最新稳定版本
sysinfo = "0.36"

# 随机数生成器 - 最新稳定版本
rand = "0.9"
rand_core = "0.6"
rand_chacha = "0.9"

# 测试工具 - 最新稳定版本
criterion = { version = "0.7", features = ["html_reports"] }
mockall = "0.13"
fake = { version = "4.3", features = ["chrono"] }
rstest = "0.26"
testcontainers = "0.25"

# 迭代器工具 - 最新稳定版本
itertools = "0.13"

# 哈希表 - 最新稳定版本
hashbrown = "0.15"

# 索引映射 - 保持兼容版本
indexmap = "2.10"

# 草图算法 - 最新稳定版本
sketches-ddsketch = "0.3"

# 配置文件处理 - 最新稳定版本
etcetera = "0.10"

# 模式匹配 - 最新稳定版本
schemars = "1.0"



[package]
name = "axum-tutorial"
version = "0.1.0"
edition = "2024"
license = "MIT OR Apache-2.0"
authors = ["Rust学习者"]
description = "Axum框架的分层架构学习项目"

[lib]
name = "axum_tutorial"
path = "tests/lib.rs"

[[bin]]
name = "test_websocket_stability_api"
path = "test_websocket_stability_api.rs"

[dependencies]
# 内部依赖
axum-server = { path = "server" }
anyhow = "1.0.82"
reqwest = { version = "0.12", features = ["json"] }

# Axum Web 框架核心组件 - 升级到 0.8.4
axum = { version = "0.8.4", features = ["ws", "macros"] }
# axum-extra = { version = "0.10.1", features = ["spa"] }
# Tokio 异步运行时 - 升级到最新稳定版本
tokio = { version = "1.45.1", features = ["full"] }
# 序列化与反序列化库 - 保持最新
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
# 唯一ID生成 - 升级到最新版本
uuid = { version = "1.11", features = ["v4", "serde"] }
# 日志与跟踪 - 升级到最新版本，增强错误处理和日志记录功能
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json", "time", "local-time"] }
# 错误跟踪和上下文
tracing-error = "0.2"
# 日志文件轮转和管理
tracing-appender = "0.2"
# HTTP 工具库 - 升级到与 Axum 0.8.4 兼容的版本
tower = { version = "0.5.2", features = ["util", "timeout"] }
tower-http = { version = "0.6.6", features = ["trace", "cors", "fs", "timeout"] }
# HTTP/3 支持（可选功能）- 暂时注释掉以简化升级过程
# quinn = "0.11"
# h3 = "0.0.6"
# h3-quinn = "0.0.7"
# TLS 支持 - 暂时注释掉以简化升级过程
# rustls = "0.23"
# tokio-rustls = "0.26"
# 证书生成工具（用于 HTTP/3 演示）- 暂时注释掉以简化升级过程
# rcgen = "0.13"
# 同步原语 - 升级到最新版本
parking_lot = "0.12"
# SeaORM - 最新稳定版本，支持PostgreSQL，使用native-tls避免aws-lc-sys编译问题
sea-orm = { version = "1.1", features = ["sqlx-postgres", "sqlx-sqlite", "runtime-tokio-native-tls", "mock"] }
# Fred - DragonflyDB/Redis客户端 - 最新稳定版本
fred = { version = "10.1" }
# 迁移 Crate
migration = { path = "migration" }
# 内部 Crates
app_common = { path = "crates/app_common" }
app_domain = { path = "crates/app_domain" }
app_application = { path = "crates/app_application" }
app_infrastructure = { path = "crates/app_infrastructure" }
app_interfaces = { path = "crates/app_interfaces" }
# 异步 Trait 支持 - 在 Axum 0.8.4 中仍然需要用于 dyn compatible traits
async-trait = "0.1"
# Futures 工具 - 用于 WebSocket 流处理
futures-util = "0.3"
# 性能监控和指标收集
metrics = "0.24"
metrics-exporter-prometheus = "0.17"
# 系统信息收集
sysinfo = "0.36"
# 错误恢复机制依赖 - 替换未维护的backoff包
tokio-retry = "0.3"  # 指数退避重试 (替代backoff)
failsafe = "1.0"  # 断路器模式和错误恢复

# --- 认证与验证功能新增依赖 ---
# 输入验证 - 升级到0.20修复RUSTSEC-2024-0421 (idna漏洞)
validator = { version = "0.20", features = ["derive"] }
# 密码哈希 - 使用 Argon2 (更安全的现代密码哈希算法)
argon2 = { version = "0.5", features = ["password-hash"] }
# 随机数生成器 - 用于生成盐值
rand_core = "0.6"
# 随机数生成器 - 用于生成随机数
rand = "0.9"
# JSON Web Token (JWT)
jsonwebtoken = "9.3"
# 环境变量加载
dotenvy = "0.15"
# URL 编码解析（用于 WebSocket 查询参数）
serde_urlencoded = "0.7"
bytes = "1.10.1"
flate2 = "1.1.2"
# --- 认证与验证功能新增依赖结束 ---

# --- 测试工具依赖 (在主依赖中，但可选) ---
# 这些依赖被测试模块使用，但也可能被集成测试需要
tempfile = "3.8"
# WebSocket 测试客户端 - 用于E2E测试和并发测试
tokio-tungstenite = { version = "0.26", features = ["native-tls"] }
url = { version = "2.5", optional = true }
# 边缘情况测试依赖
futures = "0.3"

# --- 测试报告生成器依赖 ---
# HTML转义
html-escape = "0.2"
# 命令行参数解析
clap = { version = "4.4", features = ["derive"] }
# 环境日志
env_logger = "0.11"
# 主机名获取
hostname = "0.4"
chrono = { version = "0.4.41", features = ["serde"] }

# 移除重复的测试文件bin定义，让它们作为正常的集成测试运行

# 暂时禁用有chrono依赖问题的二进制文件
# [[bin]]
# name = "websocket_latency_test"
# path = "scripts/websocket_latency_test.rs"

[[bin]]
name = "verify_postgres_connection_pool"
path = "scripts/verify_postgres_connection_pool.rs"
# 移除无效的required-features配置

# [[bin]]
# name = "verify_postgres_config"
# path = "scripts/verify_postgres_config.rs"

[[bin]]
name = "test_dragonfly_connection"
path = "scripts/test_dragonfly_connection.rs"

[[bin]]
name = "test_dragonfly_direct"
path = "scripts/test_dragonfly_direct.rs"

[[bin]]
name = "test_simple_connection"
path = "test_simple_connection.rs"

# 移除更多重复的测试文件bin定义

# [[bin]]
# name = "test_report_generator"
# path = "src/bin/test_report_generator.rs"

[features]
default = []
# 测试特性，启用测试工具依赖
testing = []

[dev-dependencies]
# 基础测试依赖
anyhow = "1.0.82"
serde_json = "1.0.117"
once_cell = "1.19.0"

# 内部 Crates for testing
app_common = { path = "crates/app_common" }

# HTTP 测试工具
httpc-test = "0.1.9"
axum-test = "17.3"

# 数据库测试工具
sea-orm-cli = "1.1"

# 模拟和测试数据生成
wiremock = "0.6.0"
fake = { version = "4.3", features = ["chrono"] }
mockall = "0.13"

# WebSocket 测试 - 这些依赖现在在主依赖中通过 testing 特性提供
# tokio-tungstenite = "0.23"  # 已移动到主依赖
serde_urlencoded = "0.7"
# url = "2.5"  # 已移动到主依赖

# 异步测试工具
tokio-test = "0.4"
futures-util = "0.3"

# 日志测试
tracing-test = "0.2"

# 文件系统测试
# tempfile = "3.8"  # 已移动到主依赖，通过 testing 特性提供
filetime = "0.2"

# 性能基准测试
criterion = { version = "0.7.0", features = ["html_reports"] }
# WebSocket基准测试需要的额外依赖
uuid = { version = "1.11", features = ["v4", "serde"] }
# 基准测试报告生成依赖
chrono = { version = "0.4.41", features = ["serde"] }
# 可视化图表生成
plotters = { version = "0.3", features = ["bitmap_backend", "chrono"] }

# 测试覆盖率工具 - 应该通过 cargo install cargo-tarpaulin 安装，而不是作为依赖

# Mock和测试工具增强
mockito = "1.4"  # HTTP模拟服务器
test-log = "0.2"  # 测试日志捕获

# 测试断言增强
assert_matches = "1.5"
pretty_assertions = "1.4"
assert-json-diff = "2.0"  # JSON响应比较

# 测试并发控制
serial_test = "3.1"

# E2E测试专用依赖
# 环境变量加载（测试环境配置）
dotenvy = "0.15"
# 异步测试工具（已存在但确保版本一致）
# tokio-test = "0.4"  # 已在上面定义
# 测试容器（数据库隔离）
testcontainers = "0.25"

# YAML解析支持（用于配置文件测试）
serde_yaml = "0.9"

# 测试数据生成和模拟增强
proptest = "1.4"  # 属性测试
quickcheck = "1.0"  # 快速检查测试
rstest = "0.26"  # 参数化测试

# 基准测试配置
[[bench]]
name = "api_performance_benchmarks"
harness = false

[[bench]]
name = "websocket_latency_benchmarks"
harness = false

[[bench]]
name = "simple_benchmark_test"
harness = false

[[bench]]
name = "websocket_simulation_benchmarks"
harness = false

[[bench]]
name = "concurrent_load_benchmarks"
harness = false

[[bench]]
name = "comprehensive_performance_benchmarks"
harness = false

[[bench]]
name = "stress_test_benchmarks"
harness = false

# 基准测试报告生成器二进制文件（暂时禁用）
# [[bin]]
# name = "generate_benchmark_report"
# path = "scripts/generate_benchmark_report.rs"

# 覆盖率报告生成器二进制文件
[[bin]]
name = "generate_coverage_report"
path = "scripts/generate_coverage_report.rs"

# LCOV覆盖率解析器二进制文件
[[bin]]
name = "parse_lcov_coverage"
path = "scripts/parse_lcov_coverage.rs"

[[bin]]
name = "generate_performance_report"
path = "scripts/generate_performance_report.rs"

# 查询优化功能演示脚本 - 暂时禁用
# [[bin]]
# name = "demo_query_optimization"
# path = "demo_query_optimization.rs"

# 端到端测试执行脚本
[[bin]]
name = "run_e2e_tests"
path = "scripts/run_e2e_tests.rs"

[[bin]]
name = "stress_test_runner"
path = "scripts/stress_test_runner.rs"

[[bin]]
name = "stress_test_monitor"
path = "scripts/stress_test_monitor.rs"

[[bin]]
name = "stress_test_visualizer"
path = "scripts/stress_test_visualizer.rs"

[[bin]]
name = "stress_test_html_report"
path = "scripts/stress_test_html_report.rs"

[[bin]]
name = "comprehensive_test_report"
path = "scripts/comprehensive_test_report.rs"

[[bin]]
name = "final_acceptance_test"
path = "tests/final_acceptance_test.rs"

[[bin]]
name = "improved_acceptance_test"
path = "tests/improved_acceptance_test.rs"

[[bin]]
name = "simple_api_test"
path = "tests/simple_api_test.rs"

[[bin]]
name = "final_100_percent_test"
path = "tests/final_100_percent_test.rs"

# 系统性验收测试 - 任务ID 27
[[bin]]
name = "run_acceptance_test"
path = "tests/run_acceptance_test.rs"

[[bin]]
name = "run_system_acceptance_test"
path = "tests/run_system_acceptance_test.rs"

[[bin]]
name = "comprehensive_system_acceptance_test"
path = "tests/comprehensive_system_acceptance_test.rs"

[[bin]]
name = "playwright_system_acceptance_test"
path = "tests/playwright_system_acceptance_test.rs"


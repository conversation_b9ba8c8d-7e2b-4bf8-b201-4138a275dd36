{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 13318305459243126790, "path": 2318783664025873781, "deps": [[5103565458935487, "futures_io", false, 5151324269916930957], [1811549171721445101, "futures_channel", false, 2838162066204857206], [7013762810557009322, "futures_sink", false, 5069543334601548086], [7620660491849607393, "futures_core", false, 7854833873472762525], [10629569228670356391, "futures_util", false, 14461470804757477669], [12779779637805422465, "futures_executor", false, 6306507957928768795], [16240732885093539806, "futures_task", false, 16427088702997707522]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-44dba2cadb8d4e41\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
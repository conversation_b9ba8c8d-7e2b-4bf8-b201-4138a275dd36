//! # 缓存服务实现
//!
//! 提供统一的缓存操作接口，支持DragonflyDB/Redis
//! 实现企业级缓存功能：TTL管理、压缩、序列化等

use super::client_manager::CacheClientManager;
use anyhow::{ Result as AnyhowResult, anyhow };
use async_trait::async_trait;
use fred::prelude::*;
use serde::{ Deserialize, Serialize };

use std::sync::Arc;
use std::time::Duration;
use tracing::{ debug, error, info, warn };

/// 缓存服务trait
///
/// 【目的】: 定义统一的缓存操作接口，支持不同的缓存实现
/// 【设计】: 使用async trait，支持泛型类型的序列化和反序列化
#[async_trait]
pub trait CacheService: Send + Sync {
    /// 获取缓存值
    ///
    /// 【参数】:
    /// - key: 缓存键
    ///
    /// 【返回】: 缓存值（如果存在）
    async fn get<T>(&self, key: &str) -> AnyhowResult<Option<T>>
        where T: for<'de> Deserialize<'de> + Send;

    /// 设置缓存值
    ///
    /// 【参数】:
    /// - key: 缓存键
    /// - value: 缓存值
    /// - ttl: 生存时间（可选）
    ///
    /// 【返回】: 操作结果
    async fn set<T>(&self, key: &str, value: &T, ttl: Option<Duration>) -> AnyhowResult<()>
        where T: Serialize + Send + Sync;

    /// 删除缓存值
    ///
    /// 【参数】:
    /// - key: 缓存键
    ///
    /// 【返回】: 操作结果
    async fn delete(&self, key: &str) -> AnyhowResult<bool>;

    /// 检查缓存键是否存在
    ///
    /// 【参数】:
    /// - key: 缓存键
    ///
    /// 【返回】: 是否存在
    async fn exists(&self, key: &str) -> AnyhowResult<bool>;

    /// 设置缓存过期时间
    ///
    /// 【参数】:
    /// - key: 缓存键
    /// - ttl: 生存时间
    ///
    /// 【返回】: 操作结果
    async fn expire(&self, key: &str, ttl: Duration) -> AnyhowResult<bool>;

    /// 获取缓存键的剩余生存时间
    ///
    /// 【参数】:
    /// - key: 缓存键
    ///
    /// 【返回】: 剩余生存时间（秒），-1表示永不过期，-2表示键不存在
    async fn ttl(&self, key: &str) -> AnyhowResult<i64>;

    /// 批量获取缓存值
    ///
    /// 【参数】:
    /// - keys: 缓存键列表
    ///
    /// 【返回】: 缓存值列表
    async fn mget<T>(&self, keys: &[&str]) -> AnyhowResult<Vec<Option<T>>>
        where T: for<'de> Deserialize<'de> + Send;

    /// 批量设置缓存值
    ///
    /// 【参数】:
    /// - pairs: 键值对列表
    /// - ttl: 生存时间（可选）
    ///
    /// 【返回】: 操作结果
    async fn mset<T>(&self, pairs: &[(&str, &T)], ttl: Option<Duration>) -> AnyhowResult<()>
        where T: Serialize + Send + Sync;

    /// 清空所有缓存（谨慎使用）
    ///
    /// 【返回】: 操作结果
    async fn flush_all(&self) -> AnyhowResult<()>;
}

/// DragonflyDB缓存服务实现
///
/// 【目的】: 基于fred客户端的DragonflyDB/Redis缓存服务实现
/// 【特性】: 支持JSON序列化、压缩、键前缀等企业级功能
pub struct DragonflyCache {
    /// 缓存客户端管理器
    manager: Arc<CacheClientManager>,
}

impl DragonflyCache {
    /// 创建新的DragonflyDB缓存服务
    ///
    /// 【参数】:
    /// - manager: 缓存客户端管理器
    ///
    /// 【返回】: 缓存服务实例
    pub fn new(manager: Arc<CacheClientManager>) -> Self {
        info!("🚀 创建DragonflyDB缓存服务");
        Self { manager }
    }

    /// 构建完整的缓存键
    ///
    /// 【参数】:
    /// - key: 原始键
    ///
    /// 【返回】: 带前缀的完整键
    fn build_key(&self, key: &str) -> String {
        let prefix = &self.manager.get_config().key_prefix;
        if prefix.is_empty() {
            key.to_string()
        } else {
            format!("{prefix}:{key}")
        }
    }

    /// 序列化值为JSON字符串
    ///
    /// 【参数】:
    /// - value: 要序列化的值
    ///
    /// 【返回】: JSON字符串
    fn serialize_value<T>(&self, value: &T) -> AnyhowResult<String> where T: Serialize {
        app_common::serde_json::to_string(value).map_err(|e| anyhow!("序列化缓存值失败: {}", e))
    }

    /// 反序列化JSON字符串为值
    ///
    /// 【参数】:
    /// - json: JSON字符串
    ///
    /// 【返回】: 反序列化的值
    fn deserialize_value<T>(&self, json: &str) -> AnyhowResult<T> where T: for<'de> Deserialize<'de> {
        // 首先验证JSON字符串是否为空或无效
        if json.is_empty() {
            return Err(anyhow!("缓存值为空字符串，无法反序列化"));
        }

        // 尝试反序列化，提供详细的错误信息
        match app_common::serde_json::from_str(json) {
            Ok(value) => Ok(value),
            Err(e) => {
                // 记录详细的错误信息用于调试
                error!("反序列化失败 - JSON内容: '{}', 错误: {}",
                    json.chars().take(200).collect::<String>(), e);

                // 检查是否是常见的数据类型不匹配问题
                if json.starts_with('"') && json.ends_with('"') {
                    // 可能是字符串被错误地存储为JSON字符串
                    warn!("检测到可能的字符串序列化问题，尝试清理缓存键");
                } else if !json.starts_with('{') && !json.starts_with('[') {
                    // 可能是非JSON数据
                    warn!("检测到非JSON格式数据，建议清理缓存");
                }

                Err(anyhow!("反序列化缓存值失败: {} (JSON前200字符: {})",
                    e, json.chars().take(200).collect::<String>()))
            }
        }
    }

    /// 获取默认TTL
    ///
    /// 【返回】: 默认TTL（秒）
    fn get_default_ttl(&self) -> u64 {
        self.manager.get_config().default_ttl
    }
}

#[async_trait]
impl CacheService for DragonflyCache {
    /// 获取缓存值
    async fn get<T>(&self, key: &str) -> AnyhowResult<Option<T>>
        where T: for<'de> Deserialize<'de> + Send
    {
        let full_key = self.build_key(key);
        debug!("获取缓存值: {}", full_key);

        let client = self.manager.get_client();

        match client.get::<String, _>(&full_key).await {
            Ok(json) if !json.is_empty() => {
                debug!("缓存命中: {}", full_key);
                let value = self.deserialize_value(&json)?;
                Ok(Some(value))
            }
            Ok(_) => {
                debug!("缓存未命中: {}", full_key);
                Ok(None)
            }
            Err(e) => {
                error!("获取缓存值失败 '{}': {}", full_key, e);
                Err(anyhow!("获取缓存值失败: {}", e))
            }
        }
    }

    /// 设置缓存值
    async fn set<T>(&self, key: &str, value: &T, ttl: Option<Duration>) -> AnyhowResult<()>
        where T: Serialize + Send + Sync
    {
        let full_key = self.build_key(key);
        let json = self.serialize_value(value)?;
        let ttl_seconds = ttl.map(|d| d.as_secs()).unwrap_or(self.get_default_ttl());

        debug!("设置缓存值: {} (TTL: {}秒)", full_key, ttl_seconds);

        let client = self.manager.get_client();

        // 使用SET命令设置带过期时间的值
        match
            client.set::<(), _, _>(
                &full_key,
                json,
                Some(Expiration::EX(ttl_seconds as i64)),
                None,
                false
            ).await
        {
            Ok(_) => {
                debug!("缓存设置成功: {}", full_key);
                Ok(())
            }
            Err(e) => {
                error!("设置缓存值失败 '{}': {}", full_key, e);
                Err(anyhow!("设置缓存值失败: {}", e))
            }
        }
    }

    /// 删除缓存值
    async fn delete(&self, key: &str) -> AnyhowResult<bool> {
        let full_key = self.build_key(key);
        debug!("删除缓存值: {}", full_key);

        let client = self.manager.get_client();

        match client.del::<i64, _>(&full_key).await {
            Ok(deleted_count) => {
                let was_deleted = deleted_count > 0;
                debug!("缓存删除结果: {} (删除数量: {})", full_key, deleted_count);
                Ok(was_deleted)
            }
            Err(e) => {
                error!("删除缓存值失败 '{}': {}", full_key, e);
                Err(anyhow!("删除缓存值失败: {}", e))
            }
        }
    }

    /// 检查缓存键是否存在
    async fn exists(&self, key: &str) -> AnyhowResult<bool> {
        let full_key = self.build_key(key);
        debug!("检查缓存键是否存在: {}", full_key);

        let client = self.manager.get_client();

        match client.exists::<i64, _>(&full_key).await {
            Ok(exists_count) => {
                let exists = exists_count > 0;
                debug!("缓存键存在性检查结果: {} -> {}", full_key, exists);
                Ok(exists)
            }
            Err(e) => {
                error!("检查缓存键存在性失败 '{}': {}", full_key, e);
                Err(anyhow!("检查缓存键存在性失败: {}", e))
            }
        }
    }

    /// 设置缓存过期时间
    async fn expire(&self, key: &str, ttl: Duration) -> AnyhowResult<bool> {
        let full_key = self.build_key(key);
        let ttl_seconds = ttl.as_secs();
        debug!("设置缓存过期时间: {} (TTL: {}秒)", full_key, ttl_seconds);

        let client = self.manager.get_client();

        match client.expire::<i64, _>(&full_key, ttl_seconds as i64, None).await {
            Ok(result) => {
                let success = result > 0;
                debug!("设置过期时间结果: {} -> {}", full_key, success);
                Ok(success)
            }
            Err(e) => {
                error!("设置缓存过期时间失败 '{}': {}", full_key, e);
                Err(anyhow!("设置缓存过期时间失败: {}", e))
            }
        }
    }

    /// 获取缓存键的剩余生存时间
    async fn ttl(&self, key: &str) -> AnyhowResult<i64> {
        let full_key = self.build_key(key);
        debug!("获取缓存键剩余生存时间: {}", full_key);

        let client = self.manager.get_client();

        match client.ttl::<i64, _>(&full_key).await {
            Ok(ttl_seconds) => {
                debug!("缓存键剩余生存时间: {} -> {}秒", full_key, ttl_seconds);
                Ok(ttl_seconds)
            }
            Err(e) => {
                error!("获取缓存键剩余生存时间失败 '{}': {}", full_key, e);
                Err(anyhow!("获取缓存键剩余生存时间失败: {}", e))
            }
        }
    }

    /// 批量获取缓存值
    async fn mget<T>(&self, keys: &[&str]) -> AnyhowResult<Vec<Option<T>>>
        where T: for<'de> Deserialize<'de> + Send
    {
        if keys.is_empty() {
            return Ok(vec![]);
        }

        let full_keys: Vec<String> = keys
            .iter()
            .map(|k| self.build_key(k))
            .collect();
        debug!("批量获取缓存值: {:?}", full_keys);

        let client = self.manager.get_client();
        let full_keys_clone = full_keys.clone(); // 克隆用于错误处理

        match client.mget::<Vec<String>, _>(full_keys).await {
            Ok(json_values) => {
                let mut results = Vec::with_capacity(json_values.len());

                for (i, json) in json_values.into_iter().enumerate() {
                    if json.is_empty() {
                        results.push(None);
                    } else {
                        match self.deserialize_value(&json) {
                            Ok(value) => results.push(Some(value)),
                            Err(e) => {
                                warn!("反序列化批量缓存值失败 '{}': {}", full_keys_clone[i], e);
                                results.push(None);
                            }
                        }
                    }
                }

                debug!(
                    "批量获取缓存值完成，成功获取 {}/{} 个值",
                    results
                        .iter()
                        .filter(|r| r.is_some())
                        .count(),
                    results.len()
                );
                Ok(results)
            }
            Err(e) => {
                error!("批量获取缓存值失败: {}", e);
                Err(anyhow!("批量获取缓存值失败: {}", e))
            }
        }
    }

    /// 批量设置缓存值
    async fn mset<T>(&self, pairs: &[(&str, &T)], ttl: Option<Duration>) -> AnyhowResult<()>
        where T: Serialize + Send + Sync
    {
        if pairs.is_empty() {
            return Ok(());
        }

        debug!("批量设置缓存值，数量: {}", pairs.len());

        // 序列化所有值
        let mut key_value_pairs = Vec::with_capacity(pairs.len());
        for (key, value) in pairs {
            let full_key = self.build_key(key);
            let json = self.serialize_value(value)?;
            key_value_pairs.push((full_key, json));
        }

        let client = self.manager.get_client();

        // 使用MSET设置所有键值对
        let mset_pairs: Vec<(&str, &str)> = key_value_pairs
            .iter()
            .map(|(k, v)| (k.as_str(), v.as_str()))
            .collect();

        match client.mset(mset_pairs).await {
            Ok(_) => {
                debug!("批量设置缓存值成功");

                // 如果指定了TTL，需要为每个键单独设置过期时间
                if let Some(ttl) = ttl {
                    let ttl_seconds = ttl.as_secs() as i64;
                    for (full_key, _) in &key_value_pairs {
                        if let Err(e) = client.expire::<i64, _>(full_key, ttl_seconds, None).await {
                            warn!("设置批量缓存键过期时间失败 '{}': {}", full_key, e);
                        }
                    }
                }

                Ok(())
            }
            Err(e) => {
                error!("批量设置缓存值失败: {}", e);
                Err(anyhow!("批量设置缓存值失败: {}", e))
            }
        }
    }

    /// 清空所有缓存（谨慎使用）
    async fn flush_all(&self) -> AnyhowResult<()> {
        warn!("⚠️  正在清空所有缓存数据！");

        let client = self.manager.get_client();

        match client.flushall::<()>(false).await {
            Ok(_) => {
                warn!("✅ 所有缓存数据已清空");
                Ok(())
            }
            Err(e) => {
                error!("清空缓存数据失败: {}", e);
                Err(anyhow!("清空缓存数据失败: {}", e))
            }
        }
    }
}

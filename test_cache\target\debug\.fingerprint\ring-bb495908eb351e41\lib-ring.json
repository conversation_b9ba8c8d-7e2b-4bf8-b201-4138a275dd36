{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"less-safe-getrandom-custom-or-rdrand\", \"less-safe-getrandom-espidf\", \"slow_tests\", \"std\", \"test_logging\", \"unstable-testing-arm-no-hw\", \"unstable-testing-arm-no-neon\", \"wasm32_unknown_unknown_js\"]", "target": 13947150742743679355, "profile": 15657897354478470176, "path": 10564980583905610822, "deps": [[2828590642173593838, "cfg_if", false, 16854446987210930884], [5491919304041016563, "build_script_build", false, 12413917211092206036], [8995469080876806959, "untrusted", false, 18246531342085428002], [9920160576179037441, "getrandom", false, 5211009567755188258]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ring-bb495908eb351e41\\dep-lib-ring", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
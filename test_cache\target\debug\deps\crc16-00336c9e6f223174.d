D:\ceshi\ceshi\axum-tutorial\test_cache\target\debug\deps\crc16-00336c9e6f223174.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\crc16-0.4.0\src\lib.rs D:\ceshi\ceshi\axum-tutorial\test_cache\target\debug\build\crc16-79066bf31a37ac6d\out/tables.rs

D:\ceshi\ceshi\axum-tutorial\test_cache\target\debug\deps\libcrc16-00336c9e6f223174.rlib: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\crc16-0.4.0\src\lib.rs D:\ceshi\ceshi\axum-tutorial\test_cache\target\debug\build\crc16-79066bf31a37ac6d\out/tables.rs

D:\ceshi\ceshi\axum-tutorial\test_cache\target\debug\deps\libcrc16-00336c9e6f223174.rmeta: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\crc16-0.4.0\src\lib.rs D:\ceshi\ceshi\axum-tutorial\test_cache\target\debug\build\crc16-79066bf31a37ac6d\out/tables.rs

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\crc16-0.4.0\src\lib.rs:
D:\ceshi\ceshi\axum-tutorial\test_cache\target\debug\build\crc16-79066bf31a37ac6d\out/tables.rs:

# env-dep:OUT_DIR=D:\\ceshi\\ceshi\\axum-tutorial\\test_cache\\target\\debug\\build\\crc16-79066bf31a37ac6d\\out

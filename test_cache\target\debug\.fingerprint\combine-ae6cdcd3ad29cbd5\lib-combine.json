{"rustc": 1842507548689473721, "features": "[\"alloc\", \"bytes\", \"futures-core-03\", \"pin-project-lite\", \"std\", \"tokio\", \"tokio-dep\", \"tokio-util\"]", "declared_features": "[\"alloc\", \"bytes\", \"bytes_05\", \"default\", \"futures-03\", \"futures-core-03\", \"futures-io-03\", \"mp4\", \"pin-project\", \"pin-project-lite\", \"regex\", \"std\", \"tokio\", \"tokio-02\", \"tokio-02-dep\", \"tokio-03\", \"tokio-03-dep\", \"tokio-dep\", \"tokio-util\"]", "target": 2090804380371586739, "profile": 15657897354478470176, "path": 14629504050667270556, "deps": [[1288403060204016458, "tokio_util", false, 6831014789819728146], [1906322745568073236, "pin_project_lite", false, 14517650215215656341], [7620660491849607393, "futures_core_03", false, 7854833873472762525], [12944427623413450645, "tokio_dep", false, 13112340756356489184], [15932120279885307830, "memchr", false, 5562783943420941609], [16066129441945555748, "bytes", false, 7309104518947092226]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\combine-ae6cdcd3ad29cbd5\\dep-lib-combine", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 1842507548689473721, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 4238072945738240332, "deps": [[1988483478007900009, "unicode_ident", false, 15491002494448767931], [3060637413840920116, "proc_macro2", false, 15270274785603497869], [17990358020177143287, "quote", false, 16161981345171341529]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-3c46c64665e1a85e\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
//! 简单的DragonflyDB连接测试

use fred::prelude::*;
use std::time::Duration;
use fred::prelude::*;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🔍 测试DragonflyDB连接...");

    // 测试不同的连接URL
    let test_urls = vec![
        "redis://:dragonfly_secure_password_2025@127.0.0.1:6379",
        "redis://:dragonfly_secure_password_2025@localhost:6379",
        "redis://127.0.0.1:6379",
        "redis://localhost:6379"
    ];

    for url in test_urls {
        println!("\n📡 尝试连接: {}", url);

        match test_connection(url).await {
            Ok(_) => {
                println!("✅ 连接成功: {}", url);
                return Ok(());
            }
            Err(e) => {
                println!("❌ 连接失败: {} - {}", url, e);
            }
        }
    }

    println!("\n❌ 所有连接尝试都失败了");
    Ok(())
}

async fn test_connection(url: &str) -> Result<(), Box<dyn std::error::Error>> {
    let config = Config::from_url(url)?;
    let client = Builder::from_config(config).build()?;

    // 设置连接超时
    tokio::time::timeout(Duration::from_secs(10), async {
        client.connect();
        client.wait_for_connect().await?;

        // 测试ping命令
        let result: String = client.ping::<String>(None).await?;
        println!("  📍 PING响应: {}", result);

        // 测试设置和获取
        let _: () = client.set("test_key", "test_value", None, None, false).await?;
        let value: String = client.get("test_key").await?;
        println!("  📍 SET/GET测试: {}", value);

        // 清理测试键
        let _: i64 = client.del("test_key").await?;

        client.quit().await?;
        Ok::<(), Box<dyn std::error::Error>>(())
    }).await??;

    Ok(())
}

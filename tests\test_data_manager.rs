//! # 测试数据管理器
//!
//! 提供测试数据的生成、管理、清理和版本控制功能。
//! 支持大规模测试数据生成，确保测试数据的一致性和隔离性。
//!
//! ## 功能特性
//! - 大规模测试数据生成
//! - 测试数据版本管理
//! - 数据隔离和清理
//! - 性能优化的批量操作

use anyhow::Result;
use chrono::{ DateTime, Utc };
use fred::prelude::*;
use fred::interfaces::KeysInterface;
use fred::types::scan::Scanner;
use futures_util::StreamExt;
use sea_orm::{ ActiveModelTrait, DatabaseConnection };
use serde::{ Deserialize, Serialize };
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{ debug, info, warn };
use uuid::Uuid;

/// 测试数据管理器
pub struct TestDataManager {
    /// 数据库连接
    db_connection: Arc<DatabaseConnection>,
    /// 缓存客户端
    cache_client: Arc<Client>,
    /// 数据集合
    datasets: Arc<RwLock<HashMap<String, TestDataset>>>,
    /// 配置
    config: TestDataConfig,
}

/// 测试数据配置
#[derive(Debug, Clone)]
pub struct TestDataConfig {
    /// 默认数据集大小
    pub default_dataset_size: usize,
    /// 批量操作大小
    pub batch_size: usize,
    /// 数据保留时间
    pub data_retention: chrono::Duration,
    /// 是否启用数据压缩
    pub enable_compression: bool,
}

impl Default for TestDataConfig {
    fn default() -> Self {
        Self {
            default_dataset_size: 10000,
            batch_size: 1000,
            data_retention: chrono::Duration::hours(24),
            enable_compression: false,
        }
    }
}

/// 测试数据集
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestDataset {
    /// 数据集ID
    pub id: String,
    /// 数据集名称
    pub name: String,
    /// 数据集描述
    pub description: String,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 数据大小
    pub size: usize,
    /// 数据版本
    pub version: u32,
    /// 数据标签
    pub tags: Vec<String>,
    /// 数据项
    pub items: Vec<TestDataItem>,
}

/// 测试数据项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestDataItem {
    /// 数据项ID
    pub id: Uuid,
    /// 数据类型
    pub data_type: TestDataType,
    /// 数据内容
    pub content: serde_json::Value,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 元数据
    pub metadata: HashMap<String, String>,
}

/// 测试数据类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TestDataType {
    /// 用户数据
    User,
    /// 消息数据
    Message,
    /// 聊天室数据
    ChatRoom,
    /// 任务数据
    Task,
    /// 自定义数据
    Custom(String),
}

impl TestDataManager {
    /// 创建新的测试数据管理器
    pub fn new(db_connection: Arc<DatabaseConnection>, cache_client: Arc<Client>) -> Self {
        Self::with_config(db_connection, cache_client, TestDataConfig::default())
    }

    /// 使用自定义配置创建测试数据管理器
    pub fn with_config(
        db_connection: Arc<DatabaseConnection>,
        cache_client: Arc<Client>,
        config: TestDataConfig
    ) -> Self {
        Self {
            db_connection,
            cache_client,
            datasets: Arc::new(RwLock::new(HashMap::new())),
            config,
        }
    }

    /// 生成消息搜索测试数据集
    pub async fn generate_message_search_dataset(&self, size: usize) -> Result<String> {
        info!("生成消息搜索测试数据集，大小: {}", size);

        let dataset_id = format!("message_search_{}", Uuid::new_v4());
        let mut items = Vec::with_capacity(size);

        // 生成测试消息数据
        for i in 0..size {
            let message_content = self.generate_message_content(i);
            let item = TestDataItem {
                id: Uuid::new_v4(),
                data_type: TestDataType::Message,
                content: serde_json::json!({
                    "id": Uuid::new_v4(),
                    "content": message_content,
                    "sender_id": Uuid::new_v4(),
                    "chat_room_id": Uuid::new_v4(),
                    "message_type": "text",
                    "created_at": Utc::now(),
                    "priority": i % 10,
                    "is_pinned": i % 100 == 0,
                }),
                created_at: Utc::now(),
                metadata: {
                    let mut meta = HashMap::new();
                    meta.insert("index".to_string(), i.to_string());
                    meta.insert("category".to_string(), "search_test".to_string());
                    meta.insert("language".to_string(), "zh-CN".to_string());
                    meta
                },
            };
            items.push(item);
        }

        let dataset = TestDataset {
            id: dataset_id.clone(),
            name: "消息搜索测试数据集".to_string(),
            description: format!("包含{}条消息的搜索测试数据集", size),
            created_at: Utc::now(),
            size,
            version: 1,
            tags: vec!["message".to_string(), "search".to_string(), "test".to_string()],
            items,
        };

        // 保存数据集
        self.save_dataset(dataset).await?;

        info!("消息搜索测试数据集生成完成: {}", dataset_id);
        Ok(dataset_id)
    }

    /// 生成消息内容
    fn generate_message_content(&self, index: usize) -> String {
        let templates = vec![
            "这是第{}条测试消息，用于验证搜索功能的准确性",
            "消息内容{}：包含中文分词测试和全文搜索验证",
            "测试消息{}：支持模糊匹配、精确搜索和性能测试",
            "搜索测试消息{}：验证PostgreSQL GIN索引和tsvector优化",
            "高并发测试消息{}：用于压力测试和性能基准测试",
            "缓存测试消息{}：验证DragonflyDB多级缓存命中率",
            "防雪崩测试消息{}：测试熔断器和限流器功能",
            "异步队列测试消息{}：验证Tokio异步处理能力"
        ];

        let template = &templates[index % templates.len()];
        template.replace("{}", &index.to_string())
    }

    /// 保存数据集
    pub async fn save_dataset(&self, dataset: TestDataset) -> Result<()> {
        debug!("保存测试数据集: {}", dataset.id);

        // 保存到内存
        {
            let mut datasets = self.datasets.write().await;
            datasets.insert(dataset.id.clone(), dataset.clone());
        }

        // 保存到缓存（用于快速访问）
        let cache_key = format!("test:dataset:{}", dataset.id);
        let serialized = serde_json::to_string(&dataset)?;

        let _: () = self.cache_client
            .set(&cache_key, &serialized, None, None, false).await
            .map_err(|e| anyhow::anyhow!("保存数据集到缓存失败: {}", e))?;

        debug!("数据集保存完成: {}", dataset.id);
        Ok(())
    }

    /// 获取数据集
    pub async fn get_dataset(&self, dataset_id: &str) -> Result<Option<TestDataset>> {
        debug!("获取测试数据集: {}", dataset_id);

        // 先从内存中查找
        {
            let datasets = self.datasets.read().await;
            if let Some(dataset) = datasets.get(dataset_id) {
                return Ok(Some(dataset.clone()));
            }
        }

        // 从缓存中查找
        let cache_key = format!("test:dataset:{}", dataset_id);
        let cached: Option<String> = self.cache_client
            .get(&cache_key).await
            .map_err(|e| anyhow::anyhow!("从缓存获取数据集失败: {}", e))?;

        if let Some(serialized) = cached {
            let dataset: TestDataset = serde_json::from_str(&serialized)?;

            // 更新内存缓存
            {
                let mut datasets = self.datasets.write().await;
                datasets.insert(dataset_id.to_string(), dataset.clone());
            }

            return Ok(Some(dataset));
        }

        Ok(None)
    }

    /// 列出所有数据集
    pub async fn list_datasets(&self) -> Result<Vec<String>> {
        let datasets = self.datasets.read().await;
        Ok(datasets.keys().cloned().collect())
    }

    /// 删除数据集
    pub async fn delete_dataset(&self, dataset_id: &str) -> Result<()> {
        info!("删除测试数据集: {}", dataset_id);

        // 从内存中删除
        {
            let mut datasets = self.datasets.write().await;
            datasets.remove(dataset_id);
        }

        // 从缓存中删除
        let cache_key = format!("test:dataset:{}", dataset_id);
        let _: i64 = self.cache_client
            .del(&cache_key).await
            .map_err(|e| anyhow::anyhow!("从缓存删除数据集失败: {}", e))?;

        info!("数据集删除完成: {}", dataset_id);
        Ok(())
    }

    /// 清理过期数据集
    pub async fn cleanup_expired_datasets(&self) -> Result<usize> {
        info!("清理过期测试数据集");

        let cutoff_time = Utc::now() - self.config.data_retention;
        let mut deleted_count = 0;

        let dataset_ids: Vec<String> = {
            let datasets = self.datasets.read().await;
            datasets
                .values()
                .filter(|dataset| dataset.created_at < cutoff_time)
                .map(|dataset| dataset.id.clone())
                .collect()
        };

        for dataset_id in dataset_ids {
            if let Err(e) = self.delete_dataset(&dataset_id).await {
                warn!("删除过期数据集失败 {}: {}", dataset_id, e);
            } else {
                deleted_count += 1;
            }
        }

        info!("清理完成，删除了 {} 个过期数据集", deleted_count);
        Ok(deleted_count)
    }

    /// 清理所有测试数据
    pub async fn cleanup_all_test_data(&self) -> Result<()> {
        info!("清理所有测试数据");

        let dataset_ids = self.list_datasets().await?;

        for dataset_id in dataset_ids {
            if let Err(e) = self.delete_dataset(&dataset_id).await {
                warn!("删除数据集失败 {}: {}", dataset_id, e);
            }
        }

        // 清理缓存中的测试数据
        let mut scan_stream = self.cache_client.scan("test:*", Some(100), None);
        let mut test_keys = Vec::new();

        while let Some(result) = scan_stream.next().await {
            match result {
                Ok(scan_result) => {
                    if let Some(results) = scan_result.results() {
                        test_keys.extend(results.clone());
                    }
                }
                Err(e) => {
                    return Err(anyhow::anyhow!("获取测试键失败: {}", e));
                }
            }
        }

        if !test_keys.is_empty() {
            let _: i64 = self.cache_client
                .del(test_keys).await
                .map_err(|e| anyhow::anyhow!("删除测试键失败: {}", e))?;
        }

        info!("所有测试数据清理完成");
        Ok(())
    }
}

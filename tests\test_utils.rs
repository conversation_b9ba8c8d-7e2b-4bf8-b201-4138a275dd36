//! # 测试工具模块
//!
//! 提供通用的测试工具函数和宏，减少测试代码重复
//! 遵循Context7 MCP最佳实践和DRY原则
//!
//! ## 功能特性
//! - PostgreSQL数据库连接池管理
//! - DragonflyDB缓存连接管理
//! - 测试数据生成和清理
//! - 测试环境配置管理
//! - 性能指标收集

use anyhow::Result;
use chrono::{ DateTime, Utc };
use fred::prelude::*;
use fred::types::scan::Scanner;
use futures_util::StreamExt;
use sea_orm::{ ConnectOptions, Database, DatabaseConnection };
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use tracing;
use uuid::Uuid;

/// 测试数据库连接池管理器
///
/// 提供PostgreSQL数据库连接池管理，支持连接复用、健康检查和自动重连
#[allow(dead_code)]
pub struct TestDatabaseManager {
    connection: Arc<DatabaseConnection>,
    config: DatabaseConfig,
}

/// 数据库配置
#[derive(Debug, Clone)]
pub struct DatabaseConfig {
    pub url: String,
    pub max_connections: u32,
    pub min_connections: u32,
    pub connect_timeout: Duration,
    pub acquire_timeout: Duration,
    pub idle_timeout: Duration,
    pub max_lifetime: Duration,
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            url: std::env
                ::var("DATABASE_URL")
                .unwrap_or_else(|_| {
                    "postgres://axum_user:axum_secure_password_2025@localhost:5432/axum_tutorial".to_string()
                }),
            max_connections: 20,
            min_connections: 2,
            connect_timeout: Duration::from_secs(10),
            acquire_timeout: Duration::from_secs(10),
            idle_timeout: Duration::from_secs(30),
            max_lifetime: Duration::from_secs(300),
        }
    }
}

#[allow(dead_code)]
impl TestDatabaseManager {
    /// 创建PostgreSQL测试数据库连接池
    pub async fn new() -> Result<Self> {
        Self::with_config(DatabaseConfig::default()).await
    }

    /// 使用自定义配置创建数据库连接池
    pub async fn with_config(config: DatabaseConfig) -> Result<Self> {
        tracing::info!("初始化测试数据库连接池: {}", config.url);

        let mut opt = ConnectOptions::new(config.url.clone());
        opt.max_connections(config.max_connections)
            .min_connections(config.min_connections)
            .connect_timeout(config.connect_timeout)
            .acquire_timeout(config.acquire_timeout)
            .idle_timeout(config.idle_timeout)
            .max_lifetime(config.max_lifetime)
            .sqlx_logging(true)
            .sqlx_logging_level(tracing::log::LevelFilter::Debug);

        let connection = Database::connect(opt).await.map_err(|e|
            anyhow::anyhow!("数据库连接失败: {}", e)
        )?;

        // 验证连接
        connection.ping().await.map_err(|e| anyhow::anyhow!("数据库连接验证失败: {}", e))?;

        tracing::info!("数据库连接池初始化成功");

        Ok(Self {
            connection: Arc::new(connection),
            config,
        })
    }

    /// 获取数据库连接
    pub fn get_connection(&self) -> Arc<DatabaseConnection> {
        self.connection.clone()
    }

    /// 验证数据库连接是否正常
    pub async fn verify_connection(&self) -> Result<()> {
        self.connection.ping().await.map_err(|e| anyhow::anyhow!("数据库连接验证失败: {}", e))?;
        Ok(())
    }

    /// 获取连接池状态信息
    pub async fn get_pool_status(&self) -> Result<DatabasePoolStatus> {
        // 注意：SeaORM没有直接暴露连接池状态的API
        // 这里通过ping测试来验证连接池健康状态
        let start_time = std::time::Instant::now();
        let is_healthy = self.connection.ping().await.is_ok();
        let ping_latency = start_time.elapsed();

        Ok(DatabasePoolStatus {
            is_healthy,
            ping_latency,
            active_connections: 0, // SeaORM不提供此信息
            idle_connections: 0, // SeaORM不提供此信息
            max_connections: self.config.max_connections,
        })
    }

    /// 关闭数据库连接池
    pub async fn close(&self) -> Result<()> {
        tracing::info!("关闭数据库连接池");
        // SeaORM的Arc<DatabaseConnection>不需要显式关闭
        // 连接池会在Drop时自动清理
        Ok(())
    }
}

/// 数据库连接池状态
#[derive(Debug, Clone)]
pub struct DatabasePoolStatus {
    pub is_healthy: bool,
    pub ping_latency: Duration,
    pub active_connections: u32,
    pub idle_connections: u32,
    pub max_connections: u32,
}

/// DragonflyDB缓存连接管理器
///
/// 提供DragonflyDB缓存连接管理，支持连接池、健康检查和自动重连
#[allow(dead_code)]
pub struct TestCacheManager {
    client: Arc<Client>,
    config: CacheConfig,
}

/// 缓存配置
#[derive(Debug, Clone)]
pub struct CacheConfig {
    pub url: String,
    pub pool_size: usize,
    pub connect_timeout: Duration,
    pub command_timeout: Duration,
    pub max_retries: u32,
    pub retry_delay: Duration,
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            url: std::env
                ::var("DRAGONFLY_URL")
                .unwrap_or_else(|_| "redis://localhost:6379".to_string()),
            pool_size: 10,
            connect_timeout: Duration::from_secs(5),
            command_timeout: Duration::from_secs(5),
            max_retries: 3,
            retry_delay: Duration::from_millis(100),
        }
    }
}

#[allow(dead_code)]
impl TestCacheManager {
    /// 创建DragonflyDB缓存连接池
    pub async fn new() -> Result<Self> {
        Self::with_config(CacheConfig::default()).await
    }

    /// 使用自定义配置创建缓存连接池
    pub async fn with_config(config: CacheConfig) -> Result<Self> {
        tracing::info!("初始化DragonflyDB缓存连接池: {}", config.url);

        let redis_config = Config::from_url(&config.url).map_err(|e|
            anyhow::anyhow!("解析缓存URL失败: {}", e)
        )?;

        let policy = ReconnectPolicy::new_exponential(
            config.max_retries,
            config.retry_delay.as_millis() as u32,
            (config.retry_delay.as_millis() as u32) * 10,
            2
        );

        let client = Builder::from_config(redis_config).build()?;

        // 连接到DragonflyDB
        client.init().await.map_err(|e| anyhow::anyhow!("DragonflyDB连接失败: {}", e))?;

        // 验证连接
        let _: String = client
            .ping(None).await
            .map_err(|e| anyhow::anyhow!("DragonflyDB连接验证失败: {}", e))?;

        tracing::info!("DragonflyDB连接池初始化成功");

        Ok(Self {
            client: Arc::new(client),
            config,
        })
    }

    /// 获取缓存客户端
    pub fn get_client(&self) -> Arc<Client> {
        self.client.clone()
    }

    /// 验证缓存连接是否正常
    pub async fn verify_connection(&self) -> Result<()> {
        let _: String = self.client
            .ping(None).await
            .map_err(|e| anyhow::anyhow!("缓存连接验证失败: {}", e))?;
        Ok(())
    }

    /// 获取缓存连接状态信息
    pub async fn get_cache_status(&self) -> Result<CacheStatus> {
        let start_time = std::time::Instant::now();
        let ping_result: Result<String, _> = self.client.ping(None).await;
        let ping_latency = start_time.elapsed();

        let is_healthy = ping_result.is_ok();

        // 获取缓存信息
        let _info: String = if is_healthy {
            self.client.info(None).await.unwrap_or_default()
        } else {
            String::new()
        };

        Ok(CacheStatus {
            is_healthy,
            ping_latency,
            connected_clients: 0, // 从info中解析
            used_memory_mb: 0.0, // 从info中解析
            total_commands_processed: 0, // 从info中解析
        })
    }

    /// 清空测试缓存数据
    pub async fn flush_test_data(&self) -> Result<()> {
        tracing::info!("清空测试缓存数据");

        // 只删除测试相关的键，避免影响其他数据
        let test_pattern = "test:*";
        let mut scan_stream = self.client.scan(test_pattern, Some(100), None);
        let mut keys = Vec::new();

        while let Some(result) = scan_stream.next().await {
            match result {
                Ok(scan_result) => {
                    if let Some(results) = scan_result.results() {
                        keys.extend(results.clone());
                    }
                }
                Err(e) => {
                    return Err(anyhow::anyhow!("获取测试键失败: {}", e));
                }
            }
        }

        if !keys.is_empty() {
            let _: i64 = self.client
                .del(keys).await
                .map_err(|e| anyhow::anyhow!("删除测试键失败: {}", e))?;
            tracing::info!("已清空测试缓存数据");
        }

        Ok(())
    }

    /// 关闭缓存连接
    pub async fn close(&self) -> Result<()> {
        tracing::info!("关闭DragonflyDB连接");
        self.client.quit().await.map_err(|e| anyhow::anyhow!("关闭缓存连接失败: {}", e))?;
        Ok(())
    }
}

/// 缓存连接状态
#[derive(Debug, Clone)]
pub struct CacheStatus {
    pub is_healthy: bool,
    pub ping_latency: Duration,
    pub connected_clients: u32,
    pub used_memory_mb: f64,
    pub total_commands_processed: u64,
}

/// 测试用户数据生成器
#[allow(dead_code)]
pub struct TestUserBuilder {
    id: Option<Uuid>,
    username: String,
    email: Option<String>,
    password_hash: String,
    created_at: DateTime<Utc>,
    updated_at: DateTime<Utc>,
}

#[allow(dead_code)]
impl TestUserBuilder {
    /// 创建新的测试用户构建器
    pub fn new() -> Self {
        let now = Utc::now();
        Self {
            id: None,
            username: "test_user".to_string(),
            email: None,
            password_hash: "hashed_password".to_string(),
            created_at: now,
            updated_at: now,
        }
    }

    /// 设置用户ID
    pub fn with_id(mut self, id: Uuid) -> Self {
        self.id = Some(id);
        self
    }

    /// 设置用户名
    pub fn with_username(mut self, username: &str) -> Self {
        self.username = username.to_string();
        self
    }

    /// 设置邮箱
    pub fn with_email(mut self, email: &str) -> Self {
        self.email = Some(email.to_string());
        self
    }

    /// 设置密码哈希
    pub fn with_password_hash(mut self, password_hash: &str) -> Self {
        self.password_hash = password_hash.to_string();
        self
    }

    /// 构建测试用户数据
    pub fn build(self) -> TestUser {
        TestUser {
            id: self.id.unwrap_or_else(Uuid::new_v4),
            username: self.username,
            email: self.email,
            password_hash: self.password_hash,
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }
}

impl Default for TestUserBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// 测试用户数据结构
#[derive(Debug, Clone)]
#[allow(dead_code)]
pub struct TestUser {
    pub id: Uuid,
    pub username: String,
    pub email: Option<String>,
    pub password_hash: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 测试任务数据生成器
#[allow(dead_code)]
pub struct TestTaskBuilder {
    id: Option<Uuid>,
    title: String,
    description: Option<String>,
    completed: bool,
    user_id: Option<Uuid>,
    created_at: DateTime<Utc>,
    updated_at: DateTime<Utc>,
}

#[allow(dead_code)]
impl TestTaskBuilder {
    /// 创建新的测试任务构建器
    pub fn new() -> Self {
        let now = Utc::now();
        Self {
            id: None,
            title: "Test Task".to_string(),
            description: None,
            completed: false,
            user_id: None,
            created_at: now,
            updated_at: now,
        }
    }

    /// 设置任务ID
    pub fn with_id(mut self, id: Uuid) -> Self {
        self.id = Some(id);
        self
    }

    /// 设置任务标题
    pub fn with_title(mut self, title: &str) -> Self {
        self.title = title.to_string();
        self
    }

    /// 设置任务描述
    pub fn with_description(mut self, description: &str) -> Self {
        self.description = Some(description.to_string());
        self
    }

    /// 设置任务完成状态
    pub fn with_completed(mut self, completed: bool) -> Self {
        self.completed = completed;
        self
    }

    /// 设置用户ID
    pub fn with_user_id(mut self, user_id: Uuid) -> Self {
        self.user_id = Some(user_id);
        self
    }

    /// 构建测试任务数据
    pub fn build(self) -> TestTask {
        TestTask {
            id: self.id.unwrap_or_else(Uuid::new_v4),
            title: self.title,
            description: self.description,
            completed: self.completed,
            user_id: self.user_id.unwrap_or_else(Uuid::new_v4),
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }
}

impl Default for TestTaskBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// 测试任务数据结构
#[derive(Debug, Clone)]
#[allow(dead_code)]
pub struct TestTask {
    pub id: Uuid,
    pub title: String,
    pub description: Option<String>,
    pub completed: bool,
    pub user_id: Uuid,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 测试断言工具
#[allow(dead_code)]
pub struct TestAssertions;

#[allow(dead_code)]
impl TestAssertions {
    /// 断言UUID有效性
    pub fn assert_valid_uuid(uuid_str: &str) {
        assert!(Uuid::parse_str(uuid_str).is_ok(), "UUID字符串无效: {uuid_str}");
    }

    /// 断言时间戳在合理范围内（最近1小时内）
    pub fn assert_recent_timestamp(timestamp: DateTime<Utc>) {
        let now = Utc::now();
        let one_hour_ago = now - chrono::Duration::hours(1);
        assert!(timestamp >= one_hour_ago && timestamp <= now, "时间戳不在合理范围内: {timestamp}");
    }

    /// 断言字符串非空且长度在范围内
    pub fn assert_string_length(value: &str, min_len: usize, max_len: usize) {
        assert!(!value.is_empty(), "字符串不能为空");
        assert!(
            value.len() >= min_len && value.len() <= max_len,
            "字符串长度应在 {}-{} 之间，实际长度: {}",
            min_len,
            max_len,
            value.len()
        );
    }

    /// 断言邮箱格式有效
    pub fn assert_valid_email(email: &str) {
        assert!(email.contains('@') && email.contains('.'), "邮箱格式无效: {email}");
    }

    /// 断言密码强度
    pub fn assert_password_strength(password: &str) {
        assert!(password.len() >= 8, "密码长度至少8位，实际长度: {}", password.len());
        assert!(
            password.chars().any(|c| c.is_ascii_uppercase()),
            "密码必须包含大写字母"
        );
        assert!(
            password.chars().any(|c| c.is_ascii_lowercase()),
            "密码必须包含小写字母"
        );
        assert!(
            password.chars().any(|c| c.is_ascii_digit()),
            "密码必须包含数字"
        );
    }
}

/// 测试环境管理器
///
/// 提供测试环境的初始化、配置和清理功能
pub struct TestEnvironment {
    db_manager: Option<TestDatabaseManager>,
    cache_manager: Option<TestCacheManager>,
    env_vars: HashMap<String, String>,
}

impl TestEnvironment {
    /// 创建新的测试环境
    pub fn new() -> Self {
        Self {
            db_manager: None,
            cache_manager: None,
            env_vars: HashMap::new(),
        }
    }

    /// 设置测试环境变量
    pub fn setup_test_env(&mut self) {
        tracing::info!("设置测试环境变量");

        // 保存原始环境变量
        let vars = [
            "ENVIRONMENT",
            "DATABASE_URL",
            "DRAGONFLY_URL",
            "JWT_SECRET",
            "HTTP_ADDR",
            "LOG_LEVEL",
        ];

        for var in vars.iter() {
            if let Ok(value) = std::env::var(var) {
                self.env_vars.insert(var.to_string(), value);
            }
        }

        // 设置测试环境变量
        unsafe {
            std::env::set_var("ENVIRONMENT", "test");
            std::env::set_var(
                "DATABASE_URL",
                "postgres://axum_user:axum_secure_password_2025@localhost:5432/axum_tutorial_test"
            );
            std::env::set_var("DRAGONFLY_URL", "redis://localhost:6379");
            std::env::set_var("JWT_SECRET", "test-secret-key-for-message-search-tests-2025");
            std::env::set_var("HTTP_ADDR", "127.0.0.1:0"); // 使用随机端口
            std::env::set_var("LOG_LEVEL", "debug");
        }

        tracing::info!("测试环境变量设置完成");
    }

    /// 初始化测试数据库
    pub async fn init_database(&mut self) -> Result<Arc<DatabaseConnection>> {
        tracing::info!("初始化测试数据库");

        let db_manager = TestDatabaseManager::new().await?;
        let connection = db_manager.get_connection();
        self.db_manager = Some(db_manager);

        tracing::info!("测试数据库初始化完成");
        Ok(connection)
    }

    /// 初始化测试缓存
    pub async fn init_cache(&mut self) -> Result<Arc<Client>> {
        tracing::info!("初始化测试缓存");

        let cache_manager = TestCacheManager::new().await?;
        let client = cache_manager.get_client();
        self.cache_manager = Some(cache_manager);

        tracing::info!("测试缓存初始化完成");
        Ok(client)
    }

    /// 清理测试数据
    pub async fn cleanup_test_data(&self) -> Result<()> {
        tracing::info!("清理测试数据");

        // 清理缓存数据
        if let Some(cache_manager) = &self.cache_manager {
            cache_manager.flush_test_data().await?;
        }

        // 清理数据库数据 (这里可以添加清理数据库表的逻辑)

        tracing::info!("测试数据清理完成");
        Ok(())
    }

    /// 清理测试环境
    pub async fn cleanup(&mut self) -> Result<()> {
        tracing::info!("清理测试环境");

        // 清理测试数据
        self.cleanup_test_data().await?;

        // 关闭数据库连接
        if let Some(db_manager) = &self.db_manager {
            db_manager.close().await?;
            self.db_manager = None;
        }

        // 关闭缓存连接
        if let Some(cache_manager) = &self.cache_manager {
            cache_manager.close().await?;
            self.cache_manager = None;
        }

        // 恢复原始环境变量
        unsafe {
            for (var, _) in std::env::vars() {
                if
                    var == "ENVIRONMENT" ||
                    var == "DATABASE_URL" ||
                    var == "DRAGONFLY_URL" ||
                    var == "JWT_SECRET" ||
                    var == "HTTP_ADDR" ||
                    var == "LOG_LEVEL"
                {
                    std::env::remove_var(&var);
                }
            }

            for (var, value) in &self.env_vars {
                std::env::set_var(var, value);
            }
        }

        tracing::info!("测试环境清理完成");
        Ok(())
    }
}

/// 测试宏定义
#[macro_export]
macro_rules! assert_error_contains {
    ($result:expr, $expected:expr) => {
        match $result {
            Ok(_) => panic!("期望错误，但得到了成功结果"),
            Err(e) => {
                let error_msg = format!("{}", e);
                assert!(
                    error_msg.contains($expected),
                    "错误消息 '{}' 不包含期望的文本 '{}'",
                    error_msg,
                    $expected
                );
            }
        }
    };
}

#[macro_export]
macro_rules! assert_ok_and_extract {
    ($result:expr) => {
        match $result {
            Ok(value) => value,
            Err(e) => panic!("期望成功结果，但得到错误: {}", e),
        }
    };
}

#[macro_export]
macro_rules! create_test_user {
    () => {
        $crate::test_utils::TestUserBuilder::new().build()
    };
    ($username:expr) => {
        $crate::test_utils::TestUserBuilder::new()
            .with_username($username)
            .build()
    };
    ($username:expr, $email:expr) => {
        $crate::test_utils::TestUserBuilder::new()
            .with_username($username)
            .with_email($email)
            .build()
    };
}

#[macro_export]
macro_rules! create_test_task {
    () => {
        $crate::test_utils::TestTaskBuilder::new().build()
    };
    ($title:expr) => {
        $crate::test_utils::TestTaskBuilder::new()
            .with_title($title)
            .build()
    };
    ($title:expr, $user_id:expr) => {
        $crate::test_utils::TestTaskBuilder::new()
            .with_title($title)
            .with_user_id($user_id)
            .build()
    };
}
